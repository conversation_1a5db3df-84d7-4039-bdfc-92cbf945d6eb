@import 'tailwindcss';
@import './base.css';

/* Enhanced Dark Theme Color System */
:root {
  /* Primary Colors - Dark Green Theme */
  --color-primary: #2ea043;
  --color-primary-hover: #2c974b;
  --color-primary-light: rgba(46, 160, 67, 0.1);
  --color-primary-border: rgba(46, 160, 67, 0.3);

  /* Danger Colors */
  --color-danger: #f85149;
  --color-danger-hover: #da3633;
  --color-danger-light: rgba(248, 81, 73, 0.1);
  --color-danger-border: rgba(248, 81, 73, 0.3);

  /* Warning & Success */
  --color-warning: #d29922;
  --color-warning-light: rgba(210, 153, 34, 0.1);
  --color-success: #2ea043;
  --color-info: #58a6ff;
  --color-info-light: rgba(88, 166, 255, 0.1);

  /* Background Colors */
  --color-bg-primary: #0d1117;
  --color-bg-secondary: #161b22;
  --color-bg-tertiary: #21262d;
  --color-bg-elevated: #30363d;
  --color-bg-overlay: rgba(13, 17, 23, 0.8);

  /* Border Colors */
  --color-border-primary: #30363d;
  --color-border-secondary: #21262d;
  --color-border-muted: #484f58;
  --color-border-focus: var(--color-primary);

  /* Text Colors */
  --color-text-primary: #f0f6fc;
  --color-text-secondary: #8b949e;
  --color-text-muted: #656d76;
  --color-text-inverse: #0d1117;

  /* Ultra-Compact Spacing Scale for Professional Trading UI */
  --spacing-xs: 2px; /* Minimal gaps */
  --spacing-sm: 4px; /* Tight spacing */
  --spacing-md: 6px; /* Standard spacing */
  --spacing-lg: 8px; /* Larger spacing */
  --spacing-xl: 12px; /* Maximum spacing */
  --spacing-2xl: 16px; /* Section spacing */
  --spacing-3xl: 24px; /* Page spacing */

  /* Minimal Border Radius for Professional Look */
  --radius-none: 0px;
  --radius-xs: 1px;
  --radius-sm: 2px; /* Primary choice */
  --radius-md: 3px; /* Secondary choice */
  --radius-lg: 4px; /* Rare use */
  --radius-xl: 6px; /* Special cases */

  /* Component Heights for Compact Design */
  --input-height: 28px;
  --button-height: 28px;
  --button-lg-height: 32px;
  --header-height: 40px;
  --tab-height: 32px;
  --card-padding: 8px;

  /* Typography Scale for Dense Information */
  --text-xs: 10px; /* Labels, metadata */
  --text-sm: 11px; /* Secondary text */
  --text-base: 12px; /* Primary text */
  --text-lg: 14px; /* Headers */
  --text-xl: 16px; /* Main headers */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell',
    'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  overflow: hidden;
  font-size: var(--text-base);
  line-height: 1.3;
}

#root {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

/* Enhanced Component Styles */

/* Ultra-Compact Button Components */
.btn-primary {
  background: var(--color-primary);
  color: white;
  font-weight: 600;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-primary-border);
  transition: all 0.15s ease-out;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-height: var(--button-height);
  font-size: var(--text-sm);
  letter-spacing: 0.01em;
  line-height: 1;
}

.btn-primary:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  background: var(--color-bg-elevated);
  color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
  border-color: var(--color-border-secondary);
}

.btn-danger {
  background: var(--color-danger);
  color: white;
  font-weight: 600;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-danger-border);
  transition: all 0.15s ease-out;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-height: var(--button-height);
  font-size: var(--text-sm);
  letter-spacing: 0.01em;
  line-height: 1;
}

.btn-danger:hover {
  background: var(--color-danger-hover);
  transform: translateY(-1px);
}

.btn-danger:active {
  transform: translateY(0);
}

.btn-danger:disabled {
  background: var(--color-bg-elevated);
  color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
  border-color: var(--color-border-secondary);
}

.btn-secondary {
  background: var(--color-bg-elevated);
  color: var(--color-text-primary);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-border-primary);
  transition: all 0.15s ease-out;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-height: var(--button-height);
  font-size: var(--text-sm);
  line-height: 1;
}

.btn-secondary:hover {
  background: var(--color-bg-tertiary);
  border-color: var(--color-border-muted);
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
}

/* Ultra-Compact Input Components */
.input-field {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  color: var(--color-text-primary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--text-sm);
  line-height: 1;
  transition: all 0.15s ease-out;
  min-height: var(--input-height);
  width: 100%;
}

.input-field:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 1px var(--color-primary-light);
  background: var(--color-bg-tertiary);
}

.input-field:hover:not(:focus) {
  border-color: var(--color-border-muted);
}

.input-field::placeholder {
  color: var(--color-text-muted);
  font-size: var(--text-xs);
}

.select-field {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  color: var(--color-text-primary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--text-sm);
  line-height: 1;
  transition: all 0.15s ease-out;
  min-height: var(--input-height);
  width: 100%;
  cursor: pointer;
}

.select-field:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 1px var(--color-primary-light);
  background: var(--color-bg-tertiary);
}

.select-field:hover:not(:focus) {
  border-color: var(--color-border-muted);
}

/* Ultra-Compact Card Components */
.card {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-sm);
  padding: var(--card-padding);
  transition: all 0.15s ease-out;
}

.card:hover {
  border-color: var(--color-border-muted);
}

.card-header {
  border-bottom: 1px solid var(--color-border-primary);
  padding-bottom: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.card-header h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  line-height: 1.2;
}

/* Enhanced Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-primary);
  border-radius: var(--radius-sm);
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-muted);
}

::-webkit-scrollbar-corner {
  background: var(--color-bg-secondary);
}

/* Enhanced Animation Classes */
.fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.pulse-green {
  animation: pulseGreen 2s infinite;
}

@keyframes pulseGreen {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(46, 160, 67, 0.7);
  }
  50% {
    box-shadow: 0 0 0 12px rgba(46, 160, 67, 0);
  }
}

.pulse-red {
  animation: pulseRed 2s infinite;
}

@keyframes pulseRed {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(248, 81, 73, 0.7);
  }
  50% {
    box-shadow: 0 0 0 12px rgba(248, 81, 73, 0);
  }
}

.glow-green {
  box-shadow: 0 0 20px rgba(46, 160, 67, 0.3);
}

.glow-red {
  box-shadow: 0 0 20px rgba(248, 81, 73, 0.3);
}

/* Utility Classes */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-indicator.connected {
  background: var(--color-primary-light);
  color: var(--color-primary);
  border: 1px solid var(--color-primary-border);
}

.status-indicator.disconnected {
  background: var(--color-danger-light);
  color: var(--color-danger);
  border: 1px solid var(--color-danger-border);
}

.status-indicator.running {
  background: var(--color-primary-light);
  color: var(--color-primary);
  border: 1px solid var(--color-primary-border);
}

.status-indicator.stopped {
  background: var(--color-bg-elevated);
  color: var(--color-text-muted);
  border: 1px solid var(--color-border-primary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.active {
  background: var(--color-primary);
  animation: pulse 2s infinite;
}

.status-dot.inactive {
  background: var(--color-text-muted);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Ultra-Compact Trading Interface Styles */
.trading-button-buy {
  background: var(--color-primary);
  color: white;
  font-weight: 700;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-primary);
  transition: all 0.15s ease-out;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-height: var(--button-lg-height);
  font-size: var(--text-sm);
  letter-spacing: 0.02em;
  text-transform: uppercase;
  line-height: 1;
}

.trading-button-buy:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  border-color: var(--color-primary-hover);
}

.trading-button-buy:active {
  transform: translateY(0);
}

.trading-button-sell {
  background: var(--color-danger);
  color: white;
  font-weight: 700;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-danger);
  transition: all 0.15s ease-out;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-height: var(--button-lg-height);
  font-size: var(--text-sm);
  letter-spacing: 0.02em;
  text-transform: uppercase;
  line-height: 1;
}

.trading-button-sell:hover {
  background: var(--color-danger-hover);
  transform: translateY(-1px);
  border-color: var(--color-danger-hover);
}

.trading-button-sell:active {
  transform: translateY(0);
}

/* Asset Selection Styles */
.asset-item {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.asset-item:hover {
  background: var(--color-bg-tertiary);
  border-color: var(--color-border-muted);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.asset-item.selected {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

/* Ultra-Compact Form Group Styles */
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.form-group label {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.03em;
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

/* Ultra-Compact Header Styles */
.app-header {
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--spacing-sm) var(--spacing-xl);
  min-height: var(--header-height);
  display: flex;
  align-items: center;
}

.header-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.stat-item {
  text-align: center;
  min-width: 60px;
}

.stat-label {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--color-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.03em;
  margin-bottom: 1px;
  line-height: 1;
}

.stat-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1;
}

.stat-value.positive {
  color: var(--color-primary);
}

.stat-value.negative {
  color: var(--color-danger);
}

/* Ultra-Compact Navigation Tabs */
.nav-tabs {
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: 0 var(--spacing-xl);
  min-height: var(--tab-height);
  display: flex;
  align-items: center;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 2px solid transparent;
  font-weight: 600;
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
  transition: all 0.15s ease-out;
  cursor: pointer;
  text-decoration: none;
  height: var(--tab-height);
  line-height: 1;
}

.nav-tab:hover {
  color: var(--color-text-primary);
  border-bottom-color: var(--color-border-muted);
}

.nav-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

/* Ultra-Compact Utility Classes */
.compact-spacing {
  padding: var(--spacing-sm);
  margin: var(--spacing-xs);
}

.compact-text {
  font-size: var(--text-sm);
  line-height: 1.2;
}

.compact-icon {
  width: 12px;
  height: 12px;
}

.compact-button {
  min-height: var(--button-height);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
}

.compact-input {
  min-height: var(--input-height);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
}

.compact-card {
  padding: var(--card-padding);
  border-radius: var(--radius-sm);
}

.compact-header {
  min-height: var(--header-height);
  padding: var(--spacing-xs) var(--spacing-lg);
}

.compact-nav {
  min-height: var(--tab-height);
}

.compact-nav .nav-tab {
  height: var(--tab-height);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--text-sm);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .form-row {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-sm);
  }

  .header-stats {
    gap: var(--spacing-md);
  }

  .stat-item {
    min-width: 60px;
  }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
  }

  .header-stats {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .trading-button-buy,
  .trading-button-sell {
    min-height: 38px;
    font-size: 13px;
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .app-header {
    padding: var(--spacing-xs) var(--spacing-md);
    min-height: 50px;
  }

  .nav-tabs {
    min-height: 42px;
  }

  .card {
    padding: var(--spacing-md);
  }
}
