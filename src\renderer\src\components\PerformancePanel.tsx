import { useState, useEffect, useMemo, useCallback } from 'react'
import { Bar<PERSON>hart3, TrendingUp, Target, AlertTriangle } from 'lucide-react'

// Type definitions
interface StatsResponse {
  totalTrades: number
  winRate: number
  averageProfit: number
  averageLoss: number
  largestWin: number
  largestLoss: number
  totalProfit: number
}

interface PerformanceMetrics {
  totalTrades: number
  winRate: number
  profitFactor: number
  maxDrawdown: number
  averageWin: number
  averageLoss: number
  largestWin: number
  largestLoss: number
  consecutiveWins: number
  consecutiveLosses: number
}

// Constants
const INITIAL_EQUITY = 1000
const EQUITY_MULTIPLIER = 5.2
const MAX_DRAWDOWN_CAP = 25
const PROBABILITY_THRESHOLD = 0.01
const WIN_RATE_THRESHOLDS = {
  EXCELLENT: 70,
  GOOD: 60
} as const

// Color constants for consistent theming
const COLORS = {
  SUCCESS: 'text-green-400',
  WARNING: 'text-yellow-400',
  ERROR: 'text-red-400',
  PRIMARY: 'text-blue-400',
  SECONDARY: 'text-purple-400',
  NEUTRAL: 'text-white',
  MUTED: 'text-gray-400'
} as const

// Strategy performance constants
const STRATEGY_PERFORMANCE = {
  RSI: { trades: 18, winRate: 77.8, profit: 124.5 },
  MACD: { trades: 15, winRate: 66.7, profit: 89.3 },
  BOLLINGER: { trades: 14, winRate: 71.4, profit: 67.8 }
} as const

const PerformancePanel: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    totalTrades: 0,
    winRate: 0,
    profitFactor: 0,
    maxDrawdown: 0,
    averageWin: 0,
    averageLoss: 0,
    largestWin: 0,
    largestLoss: 0,
    consecutiveWins: 0,
    consecutiveLosses: 0
  })

  const [selectedPeriod, setSelectedPeriod] = useState('today')

  // Safe calculation utilities
  const calculateProfitFactor = useCallback(
    (averageProfit: number, averageLoss: number): number => {
      const absLoss = Math.abs(averageLoss)
      if (absLoss === 0) {
        return averageProfit > 0 ? 999 : 1
      }
      return averageProfit / absLoss
    },
    []
  )

  const calculateRiskRewardRatio = useCallback(
    (averageWin: number, averageLoss: number): string => {
      const absLoss = Math.abs(averageLoss)
      if (absLoss === 0) {
        return '∞'
      }
      return (averageWin / absLoss).toFixed(2)
    },
    []
  )

  const validateStatsResponse = (stats: unknown): StatsResponse => {
    if (!stats || typeof stats !== 'object') {
      throw new Error('Invalid stats response: not an object')
    }

    const requiredFields: (keyof StatsResponse)[] = [
      'totalTrades',
      'winRate',
      'averageProfit',
      'averageLoss',
      'largestWin',
      'largestLoss',
      'totalProfit'
    ]

    const statsObj = stats as Record<string, unknown>
    for (const field of requiredFields) {
      if (typeof statsObj[field] !== 'number') {
        throw new Error(`Invalid stats response: ${field} is not a number`)
      }
    }

    return statsObj as unknown as StatsResponse
  }

  // Calculate estimated advanced metrics from available stats
  const calculateAdvancedMetrics = useCallback((stats: StatsResponse) => {
    // Estimate max drawdown based on win rate and average loss
    // Lower win rates and higher average losses suggest higher drawdowns
    const winRateDecimal = stats.winRate / 100
    const lossRateDecimal = 1 - winRateDecimal

    // Estimate based on risk of ruin formula and consecutive losses
    const estimatedMaxDrawdown = Math.min(
      (Math.abs(stats.averageLoss) * 3 * lossRateDecimal * 100) / INITIAL_EQUITY,
      MAX_DRAWDOWN_CAP
    )

    // Estimate consecutive wins/losses based on win rate
    // Higher win rates suggest longer win streaks, lower win rates suggest longer loss streaks
    const estimatedConsecutiveWins = Math.max(
      Math.round(Math.log(PROBABILITY_THRESHOLD) / Math.log(1 - winRateDecimal)),
      Math.round(stats.totalTrades * winRateDecimal * 0.3)
    )

    const estimatedConsecutiveLosses = Math.max(
      Math.round(
        Math.log(PROBABILITY_THRESHOLD) / Math.log(winRateDecimal || PROBABILITY_THRESHOLD)
      ),
      Math.round(stats.totalTrades * lossRateDecimal * 0.2)
    )

    return {
      maxDrawdown: Math.round(estimatedMaxDrawdown * 100) / 100,
      consecutiveWins: Math.min(estimatedConsecutiveWins, Math.round(stats.totalTrades * 0.4)),
      consecutiveLosses: Math.min(estimatedConsecutiveLosses, Math.round(stats.totalTrades * 0.3))
    }
  }, [])

  const generateEquityData = useCallback(() => {
    // This function is kept for potential future use but doesn't set state in compact mode
    // The equity data is now displayed inline without storing in state
  }, [])

  const loadPerformanceData = useCallback(async () => {
    try {
      // Load real sample data with validation
      const rawStats = await window.api.sampleData.getStats()
      const stats = validateStatsResponse(rawStats)

      // Calculate advanced metrics from stats
      const advancedMetrics = calculateAdvancedMetrics(stats)

      setMetrics({
        totalTrades: stats.totalTrades,
        winRate: stats.winRate,
        profitFactor: calculateProfitFactor(stats.averageProfit, stats.averageLoss),
        maxDrawdown: advancedMetrics.maxDrawdown,
        averageWin: stats.averageProfit,
        averageLoss: stats.averageLoss,
        largestWin: stats.largestWin,
        largestLoss: stats.largestLoss,
        consecutiveWins: advancedMetrics.consecutiveWins,
        consecutiveLosses: advancedMetrics.consecutiveLosses
      })

      // Generate equity curve data
      generateEquityData()

      // Strategy and asset data is now displayed inline without state storage
    } catch (error) {
      console.error('Failed to load performance data:', error)
      // Fallback to mock data with calculated metrics
      const fallbackStats: StatsResponse = {
        totalTrades: 47,
        winRate: 72.5,
        averageProfit: 15.2,
        averageLoss: -12.8,
        largestWin: 45.6,
        largestLoss: -28.9,
        totalProfit: 281.6
      }

      const advancedMetrics = calculateAdvancedMetrics(fallbackStats)

      setMetrics({
        totalTrades: fallbackStats.totalTrades,
        winRate: fallbackStats.winRate,
        profitFactor: calculateProfitFactor(fallbackStats.averageProfit, fallbackStats.averageLoss),
        maxDrawdown: advancedMetrics.maxDrawdown,
        averageWin: fallbackStats.averageProfit,
        averageLoss: fallbackStats.averageLoss,
        largestWin: fallbackStats.largestWin,
        largestLoss: fallbackStats.largestLoss,
        consecutiveWins: advancedMetrics.consecutiveWins,
        consecutiveLosses: advancedMetrics.consecutiveLosses
      })
      generateEquityData()
    }
  }, [calculateProfitFactor, calculateAdvancedMetrics, generateEquityData])

  // This will be defined after the functions below
  useEffect(() => {
    let isMounted = true

    const loadData = async (): Promise<void> => {
      try {
        await loadPerformanceData()
      } catch (error) {
        if (isMounted) {
          console.error('Failed to load performance data in effect:', error)
        }
      }
    }

    loadData()

    return () => {
      isMounted = false
    }
  }, [loadPerformanceData, selectedPeriod]) // We'll use selectedPeriod directly for now

  const getMetricColor = (value: number, isPositive: boolean): string => {
    if (isPositive) {
      return value > 0 ? COLORS.SUCCESS : COLORS.ERROR
    } else {
      return value < 0 ? COLORS.ERROR : COLORS.SUCCESS
    }
  }

  const getWinRateColor = (winRate: number): string => {
    if (winRate >= WIN_RATE_THRESHOLDS.EXCELLENT) return COLORS.SUCCESS
    if (winRate >= WIN_RATE_THRESHOLDS.GOOD) return COLORS.WARNING
    return COLORS.ERROR
  }

  // Memoized calculations for performance
  const memoizedMetrics = useMemo(() => {
    return {
      riskRewardRatio: calculateRiskRewardRatio(metrics.averageWin, metrics.averageLoss),
      expectancy:
        (metrics.winRate / 100) * metrics.averageWin +
        (1 - metrics.winRate / 100) * metrics.averageLoss,
      currentEquity: INITIAL_EQUITY + metrics.totalTrades * EQUITY_MULTIPLIER,
      profitFactorColor: getMetricColor(metrics.profitFactor - 1, true),
      winRateColor: getWinRateColor(metrics.winRate)
    }
  }, [
    metrics.averageWin,
    metrics.averageLoss,
    metrics.winRate,
    metrics.totalTrades,
    metrics.profitFactor,
    calculateRiskRewardRatio
  ])

  // Memoized strategy data to prevent unnecessary re-renders
  const memoizedStrategyData = useMemo(
    () => [
      {
        name: 'RSI Strategy',
        trades: STRATEGY_PERFORMANCE.RSI.trades,
        winRate: STRATEGY_PERFORMANCE.RSI.winRate,
        profit: STRATEGY_PERFORMANCE.RSI.profit,
        avgTrade: STRATEGY_PERFORMANCE.RSI.profit / STRATEGY_PERFORMANCE.RSI.trades
      },
      {
        name: 'MACD Strategy',
        trades: STRATEGY_PERFORMANCE.MACD.trades,
        winRate: STRATEGY_PERFORMANCE.MACD.winRate,
        profit: STRATEGY_PERFORMANCE.MACD.profit,
        avgTrade: STRATEGY_PERFORMANCE.MACD.profit / STRATEGY_PERFORMANCE.MACD.trades
      },
      {
        name: 'Bollinger Bands',
        trades: STRATEGY_PERFORMANCE.BOLLINGER.trades,
        winRate: STRATEGY_PERFORMANCE.BOLLINGER.winRate,
        profit: STRATEGY_PERFORMANCE.BOLLINGER.profit,
        avgTrade: STRATEGY_PERFORMANCE.BOLLINGER.profit / STRATEGY_PERFORMANCE.BOLLINGER.trades
      }
    ],
    []
  )

  // Memoized periods to prevent recreation on every render
  const memoizedPeriods = useMemo(
    () => [
      { value: 'today', label: 'Today' },
      { value: 'week', label: 'This Week' },
      { value: 'month', label: 'This Month' },
      { value: 'all', label: 'All Time' }
    ],
    []
  )

  return (
    <div className="h-full overflow-y-auto" style={{ background: 'var(--color-bg-primary)' }}>
      <div className="p-[12px] space-y-[12px]">
        {/* Compact Header */}
        <div className="app-header rounded-[2px]">
          <div className="flex items-center justify-between">
            <h2
              className="text-[16px] font-bold flex items-center gap-[6px]"
              style={{ color: 'var(--color-text-primary)' }}
            >
              <BarChart3 className="w-[18px] h-[18px]" style={{ color: 'var(--color-info)' }} />
              Performance Analytics
            </h2>

            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="select-field w-[140px]"
            >
              {memoizedPeriods.map((period) => (
                <option key={period.value} value={period.value}>
                  {period.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Compact Key Metrics Grid */}
        <div className="grid grid-cols-4 gap-[8px]">
          <div
            className="p-[8px] rounded-[2px] border transition-all duration-200 hover:border-opacity-60"
            style={{
              background: 'var(--color-bg-secondary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div className="flex items-center gap-[6px]">
              <Target className="w-[16px] h-[16px]" style={{ color: 'var(--color-info)' }} />
              <div className="min-w-0 flex-1">
                <div
                  className="text-[10px] font-medium uppercase tracking-wide"
                  style={{ color: 'var(--color-text-muted)' }}
                >
                  Total Trades
                </div>
                <div
                  className="text-[18px] font-bold leading-tight"
                  style={{ color: 'var(--color-text-primary)' }}
                >
                  {metrics.totalTrades}
                </div>
              </div>
            </div>
          </div>

          <div
            className="p-[8px] rounded-[2px] border transition-all duration-200 hover:border-opacity-60"
            style={{
              background: 'var(--color-bg-secondary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div className="flex items-center gap-[6px]">
              <TrendingUp className="w-[16px] h-[16px]" style={{ color: 'var(--color-primary)' }} />
              <div className="min-w-0 flex-1">
                <div
                  className="text-[10px] font-medium uppercase tracking-wide"
                  style={{ color: 'var(--color-text-muted)' }}
                >
                  Win Rate
                </div>
                <div
                  className={`text-[18px] font-bold leading-tight ${memoizedMetrics.winRateColor}`}
                >
                  {metrics.winRate.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          <div
            className="p-[8px] rounded-[2px] border transition-all duration-200 hover:border-opacity-60"
            style={{
              background: 'var(--color-bg-secondary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div className="flex items-center gap-[6px]">
              <BarChart3 className="w-[16px] h-[16px]" style={{ color: 'var(--color-warning)' }} />
              <div className="min-w-0 flex-1">
                <div
                  className="text-[10px] font-medium uppercase tracking-wide"
                  style={{ color: 'var(--color-text-muted)' }}
                >
                  Profit Factor
                </div>
                <div
                  className={`text-[18px] font-bold leading-tight ${memoizedMetrics.profitFactorColor}`}
                >
                  {metrics.profitFactor.toFixed(2)}
                </div>
              </div>
            </div>
          </div>

          <div
            className="p-[8px] rounded-[2px] border transition-all duration-200 hover:border-opacity-60"
            style={{
              background: 'var(--color-bg-secondary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div className="flex items-center gap-[6px]">
              <AlertTriangle
                className="w-[16px] h-[16px]"
                style={{ color: 'var(--color-danger)' }}
              />
              <div className="min-w-0 flex-1">
                <div
                  className="text-[10px] font-medium uppercase tracking-wide"
                  style={{ color: 'var(--color-text-muted)' }}
                >
                  Max Drawdown
                </div>
                <div
                  className="text-[18px] font-bold leading-tight"
                  style={{ color: 'var(--color-danger)' }}
                >
                  {metrics.maxDrawdown.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Detailed Metrics */}
        <div className="grid grid-cols-2 gap-[8px]">
          {/* Win/Loss Analysis */}
          <div
            className="p-[8px] rounded-[2px] border"
            style={{
              background: 'var(--color-bg-secondary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div
              className="text-[12px] font-bold uppercase tracking-wide pb-[6px] mb-[6px] border-b"
              style={{
                color: 'var(--color-text-primary)',
                borderColor: 'var(--color-border-primary)'
              }}
            >
              Win/Loss Analysis
            </div>

            <div className="space-y-[4px]">
              <div className="flex justify-between items-center">
                <span
                  className="text-[11px] font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Average Win
                </span>
                <span className="text-[11px] font-bold" style={{ color: 'var(--color-primary)' }}>
                  +${metrics.averageWin.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span
                  className="text-[11px] font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Average Loss
                </span>
                <span className="text-[11px] font-bold" style={{ color: 'var(--color-danger)' }}>
                  ${metrics.averageLoss.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span
                  className="text-[11px] font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Largest Win
                </span>
                <span className="text-[11px] font-bold" style={{ color: 'var(--color-primary)' }}>
                  +${metrics.largestWin.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span
                  className="text-[11px] font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Largest Loss
                </span>
                <span className="text-[11px] font-bold" style={{ color: 'var(--color-danger)' }}>
                  ${metrics.largestLoss.toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* Streak Analysis */}
          <div
            className="p-[8px] rounded-[2px] border"
            style={{
              background: 'var(--color-bg-secondary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div
              className="text-[12px] font-bold uppercase tracking-wide pb-[6px] mb-[6px] border-b"
              style={{
                color: 'var(--color-text-primary)',
                borderColor: 'var(--color-border-primary)'
              }}
            >
              Streak Analysis
            </div>

            <div className="space-y-[4px]">
              <div className="flex justify-between items-center">
                <span
                  className="text-[11px] font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Consecutive Wins
                </span>
                <span className="text-[11px] font-bold" style={{ color: 'var(--color-primary)' }}>
                  {metrics.consecutiveWins}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span
                  className="text-[11px] font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Consecutive Losses
                </span>
                <span className="text-[11px] font-bold" style={{ color: 'var(--color-danger)' }}>
                  {metrics.consecutiveLosses}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span
                  className="text-[11px] font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Risk/Reward Ratio
                </span>
                <span
                  className="text-[11px] font-bold"
                  style={{ color: 'var(--color-text-primary)' }}
                >
                  1:{memoizedMetrics.riskRewardRatio}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span
                  className="text-[11px] font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Expectancy
                </span>
                <span
                  className="text-[11px] font-bold"
                  style={{
                    color:
                      memoizedMetrics.expectancy > 0
                        ? 'var(--color-primary)'
                        : 'var(--color-danger)'
                  }}
                >
                  ${memoizedMetrics.expectancy.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Equity Curve Chart */}
        <div
          className="p-[8px] rounded-[2px] border"
          style={{
            background: 'var(--color-bg-secondary)',
            borderColor: 'var(--color-border-primary)'
          }}
        >
          <div
            className="text-[12px] font-bold uppercase tracking-wide pb-[6px] mb-[6px] border-b"
            style={{
              color: 'var(--color-text-primary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            Equity Curve & Drawdown
          </div>

          <div
            className="h-[120px] rounded-[2px] flex items-center justify-center border"
            style={{
              background: 'var(--color-bg-tertiary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div className="text-center">
              <TrendingUp
                className="w-[24px] h-[24px] mx-auto mb-[6px] opacity-60"
                style={{ color: 'var(--color-primary)' }}
              />
              <p
                className="text-[12px] font-bold mb-[4px]"
                style={{ color: 'var(--color-text-primary)' }}
              >
                Equity Performance Chart
              </p>
              <p className="text-[10px] mb-[8px]" style={{ color: 'var(--color-text-secondary)' }}>
                Real-time equity curve and drawdown visualization
              </p>
              <div className="flex items-center justify-center gap-[12px] text-[10px]">
                <div className="flex items-center gap-[4px]">
                  <div
                    className="w-[6px] h-[6px] rounded-full"
                    style={{ background: 'var(--color-primary)' }}
                  ></div>
                  <span style={{ color: 'var(--color-text-secondary)' }}>
                    Equity: ${memoizedMetrics.currentEquity.toFixed(2)}
                  </span>
                </div>
                <div className="flex items-center gap-[4px]">
                  <div
                    className="w-[6px] h-[6px] rounded-full"
                    style={{ background: 'var(--color-danger)' }}
                  ></div>
                  <span style={{ color: 'var(--color-text-secondary)' }}>
                    Drawdown: {metrics.maxDrawdown.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Charts Grid */}
        <div className="grid grid-cols-2 gap-[8px]">
          {/* Strategy Performance Chart */}
          <div
            className="p-[8px] rounded-[2px] border"
            style={{
              background: 'var(--color-bg-secondary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div
              className="text-[12px] font-bold uppercase tracking-wide pb-[6px] mb-[6px] border-b"
              style={{
                color: 'var(--color-text-primary)',
                borderColor: 'var(--color-border-primary)'
              }}
            >
              Strategy Performance
            </div>

            <div
              className="h-[120px] rounded-[2px] flex items-center justify-center border"
              style={{
                background: 'var(--color-bg-tertiary)',
                borderColor: 'var(--color-border-primary)'
              }}
            >
              <div className="text-center w-full px-[8px]">
                <BarChart3
                  className="w-[20px] h-[20px] mx-auto mb-[6px] opacity-60"
                  style={{ color: 'var(--color-info)' }}
                />
                <p
                  className="text-[11px] font-bold mb-[6px]"
                  style={{ color: 'var(--color-text-primary)' }}
                >
                  Strategy Comparison
                </p>
                <div className="space-y-[2px] text-[9px]">
                  <div className="flex items-center justify-between">
                    <span style={{ color: 'var(--color-text-secondary)' }}>RSI Strategy:</span>
                    <span style={{ color: 'var(--color-primary)' }}>+$124.50</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span style={{ color: 'var(--color-text-secondary)' }}>MACD Strategy:</span>
                    <span style={{ color: 'var(--color-primary)' }}>+$89.30</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span style={{ color: 'var(--color-text-secondary)' }}>Bollinger Bands:</span>
                    <span style={{ color: 'var(--color-primary)' }}>+$67.80</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Asset Distribution Chart */}
          <div
            className="p-[8px] rounded-[2px] border"
            style={{
              background: 'var(--color-bg-secondary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            <div
              className="text-[12px] font-bold uppercase tracking-wide pb-[6px] mb-[6px] border-b"
              style={{
                color: 'var(--color-text-primary)',
                borderColor: 'var(--color-border-primary)'
              }}
            >
              Asset Distribution
            </div>

            <div
              className="h-[120px] rounded-[2px] flex items-center justify-center border"
              style={{
                background: 'var(--color-bg-tertiary)',
                borderColor: 'var(--color-border-primary)'
              }}
            >
              <div className="text-center w-full px-[8px]">
                <Target
                  className="w-[20px] h-[20px] mx-auto mb-[6px] opacity-60"
                  style={{ color: 'var(--color-warning)' }}
                />
                <p
                  className="text-[11px] font-bold mb-[6px]"
                  style={{ color: 'var(--color-text-primary)' }}
                >
                  Trading Assets
                </p>
                <div className="grid grid-cols-2 gap-[2px] text-[9px]">
                  <div className="flex items-center gap-[4px]">
                    <div
                      className="w-[6px] h-[6px] rounded-full"
                      style={{ background: 'var(--color-info)' }}
                    ></div>
                    <span style={{ color: 'var(--color-text-secondary)' }}>EUR/USD: 35%</span>
                  </div>
                  <div className="flex items-center gap-[4px]">
                    <div
                      className="w-[6px] h-[6px] rounded-full"
                      style={{ background: 'var(--color-primary)' }}
                    ></div>
                    <span style={{ color: 'var(--color-text-secondary)' }}>BTC/USD: 25%</span>
                  </div>
                  <div className="flex items-center gap-[4px]">
                    <div
                      className="w-[6px] h-[6px] rounded-full"
                      style={{ background: 'var(--color-warning)' }}
                    ></div>
                    <span style={{ color: 'var(--color-text-secondary)' }}>GBP/USD: 20%</span>
                  </div>
                  <div className="flex items-center gap-[4px]">
                    <div
                      className="w-[6px] h-[6px] rounded-full"
                      style={{ background: 'var(--color-danger)' }}
                    ></div>
                    <span style={{ color: 'var(--color-text-secondary)' }}>Others: 20%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Strategy Breakdown */}
        <div
          className="p-[8px] rounded-[2px] border"
          style={{
            background: 'var(--color-bg-secondary)',
            borderColor: 'var(--color-border-primary)'
          }}
        >
          <div
            className="text-[12px] font-bold uppercase tracking-wide pb-[6px] mb-[6px] border-b"
            style={{
              color: 'var(--color-text-primary)',
              borderColor: 'var(--color-border-primary)'
            }}
          >
            Strategy Breakdown
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-[10px]">
              <thead>
                <tr className="border-b" style={{ borderColor: 'var(--color-border-primary)' }}>
                  <th
                    className="text-left py-[4px] font-medium uppercase tracking-wide"
                    style={{ color: 'var(--color-text-muted)' }}
                  >
                    Strategy
                  </th>
                  <th
                    className="text-right py-[4px] font-medium uppercase tracking-wide"
                    style={{ color: 'var(--color-text-muted)' }}
                  >
                    Trades
                  </th>
                  <th
                    className="text-right py-[4px] font-medium uppercase tracking-wide"
                    style={{ color: 'var(--color-text-muted)' }}
                  >
                    Win Rate
                  </th>
                  <th
                    className="text-right py-[4px] font-medium uppercase tracking-wide"
                    style={{ color: 'var(--color-text-muted)' }}
                  >
                    Profit
                  </th>
                  <th
                    className="text-right py-[4px] font-medium uppercase tracking-wide"
                    style={{ color: 'var(--color-text-muted)' }}
                  >
                    Avg Trade
                  </th>
                </tr>
              </thead>
              <tbody>
                {memoizedStrategyData.map((strategy) => (
                  <tr
                    key={strategy.name}
                    className="border-b hover:bg-opacity-50 transition-colors duration-150"
                    style={{
                      borderColor: 'var(--color-border-secondary)'
                    }}
                  >
                    <td
                      className="py-[4px] font-medium"
                      style={{ color: 'var(--color-text-primary)' }}
                    >
                      {strategy.name}
                    </td>
                    <td
                      className="text-right font-bold"
                      style={{ color: 'var(--color-text-primary)' }}
                    >
                      {strategy.trades}
                    </td>
                    <td className={`text-right font-bold ${getWinRateColor(strategy.winRate)}`}>
                      {strategy.winRate}%
                    </td>
                    <td className="text-right font-bold" style={{ color: 'var(--color-primary)' }}>
                      +${strategy.profit.toFixed(2)}
                    </td>
                    <td className="text-right font-bold" style={{ color: 'var(--color-primary)' }}>
                      +${strategy.avgTrade.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PerformancePanel
