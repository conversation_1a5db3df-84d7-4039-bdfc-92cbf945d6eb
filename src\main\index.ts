import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import icon from '../../resources/icon.png?asset'

// Mock services for now to get the UI running
let mockServices = {
  database: {
    getSessions: () => Promise.resolve({ success: true, data: { data: [], total: 0 } }),
    getTrades: () => Promise.resolve({ success: true, data: { data: [], total: 0 } }),
    createLog: () => Promise.resolve({ success: true }),
    createTrade: () => Promise.resolve({ success: true })
  },
  trading: {
    getStatus: () => Promise.resolve({ isRunning: false }),
    startBot: () => Promise.resolve(true),
    stopBot: () => Promise.resolve(true)
  },
  sampleData: {
    getAssets: () =>
      Promise.resolve([
        { symbol: 'EURUSD_otc', name: 'EUR/USD', category: 'currency', isActive: true, isOTC: 1 },
        { symbol: 'BTCUSD_otc', name: 'Bitcoin', category: 'crypto', isActive: true, isOTC: 1 }
      ])
  }
}

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    show: false,
    autoHideMenuBar: true,
    titleBarStyle: 'hidden',
    backgroundColor: '#111827',
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (process.env.NODE_ENV === 'development' && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// Initialize services
async function initializeServices(): Promise<void> {
  try {
    // Initialize database service
    databaseService = new DatabaseService()
    await databaseService.initialize()
    console.log('Database service initialized')

    // Initialize sample data service
    sampleDataService = new SampleDataService()
    console.log('Sample data service initialized')

    // Initialize WebSocket service
    webSocketService = new WebSocketService()
    console.log('WebSocket service initialized')

    // Initialize trading engine
    tradingEngine = new TradingEngine(webSocketService, databaseService)
    console.log('Trading engine initialized')

    // Setup trading engine event handlers
    tradingEngine.on('started', (session) => {
      console.log('Trading session started:', session.id)
    })

    tradingEngine.on('stopped', () => {
      console.log('Trading session stopped')
    })

    tradingEngine.on('tradeExecuted', (trade) => {
      console.log('Trade executed:', trade.id)
    })

    tradingEngine.on('tradeClosed', (trade) => {
      console.log('Trade closed:', trade.id, 'Result:', trade.result)
    })
  } catch (error) {
    console.error('Failed to initialize services:', error)
    throw error
  }
}

// Setup IPC handlers
function setupIpcHandlers(): void {
  // Connection status
  ipcMain.handle('get-connection-status', async () => {
    return webSocketService.getState().isConnected
  })

  // WebSocket operations
  ipcMain.handle('ws-connect', async () => {
    return await webSocketService.connect()
  })

  ipcMain.handle('ws-disconnect', async () => {
    return await webSocketService.disconnect()
  })

  ipcMain.handle('ws-authenticate', async (_, credentials) => {
    return await webSocketService.authenticate(
      credentials.sessionToken,
      credentials.userId,
      credentials.isDemo
    )
  })

  ipcMain.handle('ws-open-order', async (_, order) => {
    return await webSocketService.openOrder(order)
  })

  ipcMain.handle('ws-close-order', async (_, orderId) => {
    return await webSocketService.closeOrder(orderId)
  })

  ipcMain.handle('ws-subscribe', async (_, asset) => {
    webSocketService.subscribeToAsset(asset)
    return true
  })

  // Trading operations
  ipcMain.handle('trading-start-bot', async (_, config) => {
    return await tradingEngine.start(config)
  })

  ipcMain.handle('trading-stop-bot', async () => {
    await tradingEngine.stop()
    return true
  })

  ipcMain.handle('trading-get-status', async () => {
    return tradingEngine.getState()
  })

  ipcMain.handle('trading-update-config', async (_, config) => {
    await tradingEngine.updateConfig(config)
    return true
  })

  // Sample data operations
  ipcMain.handle('sample-data-get-assets', async () => {
    return sampleDataService.getAssets()
  })

  ipcMain.handle('sample-data-get-ohlc', async (_, asset, timeframe) => {
    return sampleDataService.getOHLCData(asset, timeframe)
  })

  ipcMain.handle('sample-data-get-stats', async () => {
    return sampleDataService.getTradingStats()
  })

  ipcMain.handle('sample-data-get-categories', async () => {
    return sampleDataService.getCategories()
  })

  // Database operations
  ipcMain.handle('db-create-session', async (_, session) => {
    return await databaseService.createSession(session)
  })

  ipcMain.handle('db-get-sessions', async (_, query) => {
    return await databaseService.getSessions(query)
  })

  ipcMain.handle('db-create-trade', async (_, trade) => {
    return await databaseService.createTrade(trade)
  })

  ipcMain.handle('db-get-trades', async (_, query) => {
    return await databaseService.getTrades(query)
  })

  ipcMain.handle('db-create-log', async (_, log) => {
    return await databaseService.createLog(log)
  })

  ipcMain.handle('db-get-logs', async (_, query) => {
    return await databaseService.getLogs(query)
  })

  // Legacy ping handler
  ipcMain.on('ping', () => console.log('pong'))
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  if (process.platform === 'win32') {
    app.setAppUserModelId('com.electron.pocket-bot')
  }

  // Initialize services
  await initializeServices()

  // Setup IPC handlers
  setupIpcHandlers()

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', async () => {
  // Clean up services
  if (tradingEngine) {
    await tradingEngine.stop()
  }

  if (webSocketService) {
    await webSocketService.disconnect()
  }

  if (databaseService) {
    await databaseService.close()
  }

  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Handle app termination
app.on('before-quit', async () => {
  if (tradingEngine) {
    await tradingEngine.stop()
  }

  if (webSocketService) {
    await webSocketService.disconnect()
  }

  if (databaseService) {
    await databaseService.close()
  }
})
