import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import icon from '../../resources/icon.png?asset'

// Mock services for now to get the UI running
let mockServices = {
  database: {
    getSessions: () => Promise.resolve({ success: true, data: { data: [], total: 0 } }),
    getTrades: () => Promise.resolve({ success: true, data: { data: [], total: 0 } }),
    createLog: () => Promise.resolve({ success: true }),
    createTrade: () => Promise.resolve({ success: true })
  },
  trading: {
    getStatus: () => Promise.resolve({ isRunning: false }),
    startBot: () => Promise.resolve(true),
    stopBot: () => Promise.resolve(true)
  },
  sampleData: {
    getAssets: () =>
      Promise.resolve([
        { symbol: 'EURUSD_otc', name: 'EUR/USD', category: 'currency', isActive: true, isOTC: 1 },
        { symbol: 'BTCUSD_otc', name: 'Bitcoin', category: 'crypto', isActive: true, isOTC: 1 }
      ])
  }
}

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    show: false,
    autoHideMenuBar: true,
    titleBarStyle: 'hidden',
    backgroundColor: '#111827',
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (process.env.NODE_ENV === 'development' && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// Initialize mock services
async function initializeServices(): Promise<void> {
  console.log('Mock services initialized for UI development')
}

// Setup mock IPC handlers for UI development
function setupIpcHandlers(): void {
  // Connection status
  ipcMain.handle('get-connection-status', async () => true)

  // Trading operations
  ipcMain.handle('trading-start-bot', async () => true)
  ipcMain.handle('trading-stop-bot', async () => true)
  ipcMain.handle('trading-get-status', async () => ({ isRunning: false }))

  // Sample data operations
  ipcMain.handle('sample-data-get-assets', async () => mockServices.sampleData.getAssets())

  // Database operations
  ipcMain.handle('db-get-sessions', async () => mockServices.database.getSessions())
  ipcMain.handle('db-get-trades', async () => mockServices.database.getTrades())
  ipcMain.handle('db-create-log', async () => mockServices.database.createLog())
  ipcMain.handle('db-create-trade', async () => mockServices.database.createTrade())

  // Legacy ping handler
  ipcMain.on('ping', () => console.log('pong'))
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  if (process.platform === 'win32') {
    app.setAppUserModelId('com.electron.pocket-bot')
  }

  // Initialize services
  await initializeServices()

  // Setup IPC handlers
  setupIpcHandlers()

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Handle app termination
app.on('before-quit', () => {
  console.log('App terminating...')
})
