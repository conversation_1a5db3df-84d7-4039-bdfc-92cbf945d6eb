import { useState, useEffect } from 'react'
import { Play, Square, TrendingUp, TrendingDown, Settings2 } from 'lucide-react'
import {
  CompactButton,
  CompactInput,
  CompactSelect,
  CompactCard,
  CompactStatus,
  CompactFormRow,
  CompactTradingButton
} from './ui/CompactComponents'

interface TradingControlsProps {
  onDataUpdate: () => void
}

interface TradingConfig {
  tradeCapital: number
  targetProfit: number
  tradeAmount: number
  tradeDuration: string
  stopLoss: number
  martingaleMode: string
  maxTradesPerSession: number
  drawdownLimit: number
}

const TradingControls: React.FC<TradingControlsProps> = ({ onDataUpdate }) => {
  const [isRunning, setIsRunning] = useState(false)
  const [config, setConfig] = useState<TradingConfig>({
    tradeCapital: 1000,
    targetProfit: 100,
    tradeAmount: 10,
    tradeDuration: 'M1',
    stopLoss: 500,
    martingaleMode: 'none',
    maxTradesPerSession: 100,
    drawdownLimit: 20
  })

  const tradeDurations = [
    { value: 'S5', label: '5 Seconds' },
    { value: 'S15', label: '15 Seconds' },
    { value: 'S30', label: '30 Seconds' },
    { value: 'M1', label: '1 Minute' },
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' }
  ]

  const martingaleModes = [
    { value: 'none', label: 'None' },
    { value: 'martingale', label: 'Martingale' },
    { value: 'anti-martingale', label: 'Anti-Martingale' },
    { value: 'fibonacci', label: 'Fibonacci' },
    { value: 'dalembert', label: "D'Alembert" }
  ]

  useEffect(() => {
    // Check trading status periodically
    const checkStatus = async () => {
      try {
        const status = await window.api.trading.getStatus()
        setIsRunning(status.isRunning)
      } catch (error) {
        console.error('Failed to get trading status:', error)
      }
    }

    checkStatus()
    const interval = setInterval(checkStatus, 5000) // Check every 5 seconds

    return () => clearInterval(interval)
  }, [])

  const handleStartBot = async () => {
    try {
      // Start the trading engine with current config
      const success = await window.api.trading.startBot(config)
      if (success) {
        setIsRunning(true)
        onDataUpdate()

        // Log the start
        await window.api.database.createLog({
          id: `log_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: 'info',
          category: 'trading',
          message: '🚀 Trading bot started via UI',
          data: JSON.stringify(config)
        })
      } else {
        console.error('Failed to start trading bot')
      }
    } catch (error) {
      console.error('Failed to start bot:', error)
    }
  }

  const handleStopBot = async () => {
    try {
      // Stop the trading engine
      const success = await window.api.trading.stopBot()
      if (success) {
        setIsRunning(false)
        onDataUpdate()

        // Log the stop
        await window.api.database.createLog({
          id: `log_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: 'info',
          category: 'trading',
          message: '⏹️ Trading bot stopped via UI',
          data: JSON.stringify({ reason: 'manual_stop' })
        })
      }
    } catch (error) {
      console.error('Failed to stop bot:', error)
    }
  }

  const handleManualTrade = async (action: 'call' | 'put') => {
    try {
      const tradeId = `trade_${Date.now()}`
      const trade = {
        id: tradeId,
        session_id: 'manual_session',
        asset: 'EURUSD', // This should come from selected asset
        action,
        amount: config.tradeAmount,
        entry_time: new Date().toISOString(),
        entry_price: 1.0, // This should come from real market data
        status: 'pending',
        strategy: 'manual',
        confidence: 100,
        request_id: Date.now()
      }

      const result = await window.api.database.createTrade(trade)
      if (result.success) {
        onDataUpdate()

        // Log the manual trade
        await window.api.database.createLog({
          id: `log_${Date.now()}`,
          timestamp: new Date().toISOString(),
          level: 'trade',
          category: 'trading',
          message: `📈 Manual ${action.toUpperCase()} trade placed`,
          data: JSON.stringify(trade),
          trade_id: tradeId
        })
      }
    } catch (error) {
      console.error('Failed to place manual trade:', error)
    }
  }

  return (
    <div
      className="h-full p-[var(--spacing-lg)] overflow-y-auto"
      style={{ background: 'var(--color-bg-secondary)' }}
    >
      <div className="space-y-[var(--spacing-md)]">
        {/* Ultra-Compact Bot Controls */}
        <CompactCard title="Bot Controls" icon={Settings2} className="fade-in">
          <div className="space-y-[var(--spacing-sm)]">
            <CompactFormRow columns={2}>
              <CompactButton
                onClick={handleStartBot}
                disabled={isRunning}
                variant={isRunning ? 'secondary' : 'primary'}
                icon={Play}
                className={!isRunning ? 'pulse-green' : ''}
              >
                START
              </CompactButton>

              <CompactButton
                onClick={handleStopBot}
                disabled={!isRunning}
                variant={!isRunning ? 'secondary' : 'danger'}
                icon={Square}
                className={isRunning ? 'pulse-red' : ''}
              >
                STOP
              </CompactButton>
            </CompactFormRow>

            <div className="flex justify-center">
              <CompactStatus
                status={isRunning ? 'running' : 'stopped'}
                label={isRunning ? 'Running' : 'Stopped'}
              />
            </div>
          </div>
        </CompactCard>

        {/* Ultra-Compact Manual Trading */}
        <CompactCard title="Manual Trading" className="slide-in-right">
          <CompactFormRow columns={2}>
            <CompactTradingButton
              action="buy"
              onClick={() => handleManualTrade('call')}
              icon={TrendingUp}
            >
              BUY
            </CompactTradingButton>

            <CompactTradingButton
              action="sell"
              onClick={() => handleManualTrade('put')}
              icon={TrendingDown}
            >
              SELL
            </CompactTradingButton>
          </CompactFormRow>
        </CompactCard>

        {/* Ultra-Compact Trading Configuration */}
        <CompactCard title="Configuration" className="fade-in">
          <div className="space-y-[var(--spacing-sm)]">
            <CompactFormRow columns={2}>
              <CompactInput
                label="Capital"
                type="number"
                value={config.tradeCapital}
                onChange={(e) => setConfig({ ...config, tradeCapital: Number(e.target.value) })}
                min="100"
                max="100000"
              />

              <CompactInput
                label="Target"
                type="number"
                value={config.targetProfit}
                onChange={(e) => setConfig({ ...config, targetProfit: Number(e.target.value) })}
                min="10"
                max="10000"
              />
            </CompactFormRow>

            <CompactFormRow columns={2}>
              <CompactInput
                label="Amount"
                type="number"
                value={config.tradeAmount}
                onChange={(e) => setConfig({ ...config, tradeAmount: Number(e.target.value) })}
                min="1"
                max="1000"
              />

              <CompactSelect
                label="Duration"
                value={config.tradeDuration}
                onChange={(e) => setConfig({ ...config, tradeDuration: e.target.value })}
                options={tradeDurations}
              />
            </CompactFormRow>

            <CompactFormRow columns={2}>
              <CompactInput
                label="Stop Loss"
                type="number"
                value={config.stopLoss}
                onChange={(e) => setConfig({ ...config, stopLoss: Number(e.target.value) })}
                min="50"
                max="50000"
              />

              <CompactSelect
                label="Martingale"
                value={config.martingaleMode}
                onChange={(e) => setConfig({ ...config, martingaleMode: e.target.value })}
                options={martingaleModes}
              />
            </CompactFormRow>
          </div>
        </CompactCard>
      </div>
    </div>
  )
}

export default TradingControls
