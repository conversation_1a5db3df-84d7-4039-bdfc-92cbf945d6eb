import { useState, useEffect } from 'react'
import { Search, Filter, TrendingUp, Clock, DollarSign } from 'lucide-react'
import TradingChart from './TradingChart'

interface Asset {
  id: string
  name: string
  category: 'currency' | 'crypto' | 'commodities' | 'indices' | 'stocks'
  isOTC: boolean
  spread: number
  volatility: number
  isActive: boolean
  currentPrice?: number
  change24h?: number
  minTradeAmount?: number
  maxTradeAmount?: number
}

const AssetSelector: React.FC = () => {
  const [assets, setAssets] = useState<Asset[]>([])
  const [filteredAssets, setFilteredAssets] = useState<Asset[]>([])
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showOTCOnly, setShowOTCOnly] = useState(false)

  const categories = [
    { value: 'all', label: 'All Assets' },
    { value: 'currency', label: 'Currency Pairs' },
    { value: 'crypto', label: 'Cryptocurrencies' },
    { value: 'commodities', label: 'Commodities' },
    { value: 'indices', label: 'Indices' },
    { value: 'stocks', label: 'Stocks' }
  ]

  // Load real sample data
  useEffect(() => {
    const loadAssets = async () => {
      try {
        const sampleAssets = await window.api.sampleData.getAssets()

        // Convert sample data to our Asset interface
        const convertedAssets: Asset[] = sampleAssets
          .filter((asset: any) => asset.isActive)
          .slice(0, 20) // Limit to first 20 for performance
          .map((asset: any) => ({
            id: asset.symbol,
            name: asset.name,
            category: mapCategory(asset.category),
            isOTC: asset.isOTC === 1,
            spread: 0.0001 + Math.random() * 0.001, // Mock spread
            volatility: Math.random() * 3 + 0.5, // Mock volatility
            isActive: asset.isActive,
            minTradeAmount: asset.minAmount || 1,
            maxTradeAmount: asset.maxAmount || 1000,
            currentPrice: generateMockPrice(asset.symbol),
            change24h: (Math.random() - 0.5) * 4 // -2% to +2%
          }))

        setAssets(convertedAssets)
        setFilteredAssets(convertedAssets)
        if (convertedAssets.length > 0) {
          setSelectedAsset(convertedAssets[0])
        }
      } catch (error) {
        console.error('Failed to load assets:', error)
        // Fallback to mock data
        loadMockAssets()
      }
    }

    const mapCategory = (category: string): Asset['category'] => {
      const categoryMap: Record<string, Asset['category']> = {
        currency: 'currency',
        cryptocurrency: 'crypto',
        crypto: 'crypto',
        stock: 'stocks',
        stocks: 'stocks',
        index: 'indices',
        indices: 'indices',
        commodity: 'commodities',
        commodities: 'commodities'
      }
      return categoryMap[category.toLowerCase()] || 'currency'
    }

    const generateMockPrice = (symbol: string): number => {
      const priceMap: Record<string, number> = {
        EURUSD_otc: 1.0856,
        GBPUSD_otc: 1.2634,
        USDJPY_otc: 149.85,
        BTCUSD_otc: 43250,
        ETHUSD_otc: 2580,
        XAUUSD_otc: 2045,
        '#AAPL_otc': 185.5,
        '#TSLA_otc': 248.3
      }
      return priceMap[symbol] || Math.random() * 100 + 50
    }

    const loadMockAssets = () => {
      const mockAssets: Asset[] = [
        {
          id: 'EURUSD_otc',
          name: 'EUR/USD',
          category: 'currency',
          isOTC: true,
          spread: 0.0001,
          volatility: 0.8,
          isActive: true,
          minTradeAmount: 1,
          maxTradeAmount: 1000,
          currentPrice: 1.0856,
          change24h: 0.12
        },
        {
          id: 'BTCUSD_otc',
          name: 'Bitcoin',
          category: 'crypto',
          isOTC: true,
          spread: 0.01,
          volatility: 3.5,
          isActive: true,
          minTradeAmount: 1,
          maxTradeAmount: 1000,
          currentPrice: 43250.0,
          change24h: 2.45
        }
      ]
      setAssets(mockAssets)
      setFilteredAssets(mockAssets)
      setSelectedAsset(mockAssets[0])
    }

    loadAssets()
  }, [])

  useEffect(() => {
    let filtered = assets

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (asset) =>
          asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          asset.id.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter((asset) => asset.category === selectedCategory)
    }

    // Filter by OTC
    if (showOTCOnly) {
      filtered = filtered.filter((asset) => asset.isOTC)
    }

    // Only show active assets
    filtered = filtered.filter((asset) => asset.isActive)

    setFilteredAssets(filtered)
  }, [assets, searchTerm, selectedCategory, showOTCOnly])

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'currency':
        return '💱'
      case 'crypto':
        return '₿'
      case 'commodities':
        return '🥇'
      case 'indices':
        return '📊'
      case 'stocks':
        return '📈'
      default:
        return '💼'
    }
  }

  const getVolatilityColor = (volatility: number) => {
    if (volatility < 1) return 'text-green-400'
    if (volatility < 2) return 'text-yellow-400'
    return 'text-red-400'
  }

  return (
    <div className="h-full flex flex-col" style={{ background: 'var(--color-bg-primary)' }}>
      {/* Ultra-Compact Asset Selection Header */}
      <div className="app-header">
        <div className="flex items-center justify-between w-full">
          <h2
            className="text-[var(--text-lg)] font-bold leading-none"
            style={{ color: 'var(--color-text-primary)' }}
          >
            Asset Selection
          </h2>
          {selectedAsset && (
            <div className="flex items-center gap-[var(--spacing-lg)]">
              <span
                className="text-[var(--text-sm)] font-semibold leading-none"
                style={{ color: 'var(--color-text-primary)' }}
              >
                {selectedAsset.name}
              </span>
              <span
                className="text-[var(--text-lg)] font-bold leading-none"
                style={{ color: 'var(--color-text-primary)' }}
              >
                $
                {selectedAsset.currentPrice?.toFixed(selectedAsset.category === 'currency' ? 4 : 2)}
              </span>
              <div
                className={`flex items-center gap-[var(--spacing-xs)]`}
                style={{
                  color:
                    (selectedAsset.change24h || 0) >= 0
                      ? 'var(--color-primary)'
                      : 'var(--color-danger)'
                }}
              >
                <TrendingUp className="w-3 h-3" />
                <span className="text-[var(--text-xs)] font-medium leading-none">
                  {(selectedAsset.change24h || 0) >= 0 ? '+' : ''}
                  {selectedAsset.change24h?.toFixed(2)}%
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="flex-1 flex">
        {/* Ultra-Compact Asset List */}
        <div
          className="w-[320px] flex-shrink-0"
          style={{ borderRight: '1px solid var(--color-border-primary)' }}
        >
          <div
            className="p-[var(--spacing-lg)]"
            style={{ background: 'var(--color-bg-secondary)' }}
          >
            <div className="space-y-[var(--spacing-md)]">
              {/* Ultra-Compact Search and Filters */}
              <div className="space-y-[var(--spacing-sm)]">
                <div className="relative">
                  <Search
                    className="absolute left-[var(--spacing-sm)] top-1/2 transform -translate-y-1/2 w-3 h-3"
                    style={{ color: 'var(--color-text-muted)' }}
                  />
                  <input
                    type="text"
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input-field pl-[24px]"
                  />
                </div>

                <div className="grid grid-cols-1 gap-[var(--spacing-sm)]">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="select-field"
                  >
                    {categories.map((category) => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>

                  <label
                    className="flex items-center gap-[var(--spacing-sm)] text-[var(--text-sm)] font-medium"
                    style={{ color: 'var(--color-text-secondary)' }}
                  >
                    <input
                      type="checkbox"
                      checked={showOTCOnly}
                      onChange={(e) => setShowOTCOnly(e.target.checked)}
                      className="rounded-[var(--radius-xs)] w-3 h-3"
                      style={{
                        backgroundColor: 'var(--color-bg-secondary)',
                        borderColor: 'var(--color-border-primary)',
                        color: 'var(--color-primary)'
                      }}
                    />
                    <span>OTC Only</span>
                  </label>
                </div>
              </div>

              {/* Ultra-Compact Asset List */}
              <div className="space-y-[var(--spacing-xs)] max-h-[400px] overflow-y-auto">
                {filteredAssets.map((asset) => (
                  <div
                    key={asset.id}
                    onClick={() => setSelectedAsset(asset)}
                    className={`asset-item fade-in ${
                      selectedAsset?.id === asset.id ? 'selected' : ''
                    }`}
                    style={{ padding: 'var(--spacing-sm)' }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-[var(--spacing-sm)]">
                        <span className="text-[var(--text-sm)]">
                          {getCategoryIcon(asset.category)}
                        </span>
                        <div>
                          <div
                            className="font-medium text-[var(--text-sm)] leading-tight"
                            style={{ color: 'var(--color-text-primary)' }}
                          >
                            {asset.name}
                          </div>
                          <div
                            className="text-[var(--text-xs)] leading-tight"
                            style={{ color: 'var(--color-text-muted)' }}
                          >
                            {asset.id}
                          </div>
                        </div>
                      </div>

                      <div className="text-right">
                        <div
                          className="font-medium text-[var(--text-sm)] leading-tight"
                          style={{ color: 'var(--color-text-primary)' }}
                        >
                          ${asset.currentPrice?.toFixed(asset.category === 'currency' ? 4 : 2)}
                        </div>
                        <div
                          className="text-[var(--text-xs)] leading-tight"
                          style={{
                            color:
                              (asset.change24h || 0) >= 0
                                ? 'var(--color-primary)'
                                : 'var(--color-danger)'
                          }}
                        >
                          {(asset.change24h || 0) >= 0 ? '+' : ''}
                          {asset.change24h?.toFixed(2)}%
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-[var(--spacing-xs)] text-[var(--text-xs)]">
                      <div
                        className="flex items-center gap-[var(--spacing-sm)]"
                        style={{ color: 'var(--color-text-muted)' }}
                      >
                        <span>S: {asset.spread}</span>
                        <span className={getVolatilityColor(asset.volatility)}>
                          V: {asset.volatility}
                        </span>
                      </div>
                      {asset.isOTC && (
                        <span
                          className="px-[var(--spacing-xs)] py-[1px] rounded-[var(--radius-xs)] text-[var(--text-xs)] font-medium"
                          style={{
                            backgroundColor: 'var(--color-warning-light)',
                            color: 'var(--color-warning)',
                            border: '1px solid var(--color-warning)'
                          }}
                        >
                          OTC
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Trading Chart */}
        <div className="flex-1" style={{ background: 'var(--color-bg-primary)' }}>
          {selectedAsset ? (
            <TradingChart asset={selectedAsset.id} timeframe="M1" height={600} />
          ) : (
            <div className="h-full flex items-center justify-center m-4">
              <div className="card text-center">
                <Filter
                  className="w-16 h-16 mx-auto mb-4 opacity-50"
                  style={{ color: 'var(--color-text-muted)' }}
                />
                <p className="text-lg" style={{ color: 'var(--color-text-secondary)' }}>
                  Select an asset to view chart
                </p>
                <p className="text-sm mt-2" style={{ color: 'var(--color-text-muted)' }}>
                  Choose from the asset list to see real-time price data
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AssetSelector
