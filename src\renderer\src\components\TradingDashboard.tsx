import { useState, useEffect } from 'react'
import { Activity, Settings, BarChart3, FileText, Wifi, WifiO<PERSON>, <PERSON> } from 'lucide-react'
import TradingControls from './TradingControls'
import AssetSelector from './AssetSelector'
import PerformancePanel from './PerformancePanel'
import LogViewer from './LogViewer'
import ConfigPanel from './ConfigPanel'
import SignalDisplay from './SignalDisplay'
import AnalyticsDashboard from './AnalyticsDashboard'

interface TradingDashboardProps {
  isConnected: boolean
}

type ActiveTab = 'trading' | 'performance' | 'analytics' | 'logs' | 'config'

const TradingDashboard: React.FC<TradingDashboardProps> = ({ isConnected }) => {
  const [activeTab, setActiveTab] = useState<ActiveTab>('trading')
  const [balance, setBalance] = useState(1000.0)
  const [profit, setProfit] = useState(0.0)
  const [winRate, setWinRate] = useState(0)
  const [totalTrades, setTotalTrades] = useState(0)
  const [activeTrades, setActiveTrades] = useState(0)

  useEffect(() => {
    // Load initial data
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // Get current session data
      const sessions = await window.api.database.getSessions({ isActive: true, limit: 1 })
      if (sessions.success && sessions.data.data.length > 0) {
        const currentSession = sessions.data.data[0]
        setBalance(currentSession.final_balance || currentSession.initial_balance)
        setProfit(currentSession.total_profit)
        setWinRate(currentSession.win_rate)
        setTotalTrades(currentSession.total_trades)
      }

      // Get active trades count
      const trades = await window.api.database.getTrades({ status: 'active' })
      if (trades.success) {
        setActiveTrades(trades.data.data.length)
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    }
  }

  const tabs = [
    { id: 'trading' as const, label: 'Trading', icon: Activity },
    { id: 'performance' as const, label: 'Performance', icon: BarChart3 },
    { id: 'analytics' as const, label: 'Analytics', icon: Brain },
    { id: 'logs' as const, label: 'Logs', icon: FileText },
    { id: 'config' as const, label: 'Settings', icon: Settings }
  ]

  return (
    <div className="flex flex-col h-full" style={{ background: 'var(--color-bg-primary)' }}>
      {/* Ultra-Compact Header */}
      <header className="app-header">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-[var(--spacing-lg)]">
            <h1
              className="text-[var(--text-xl)] font-bold leading-none"
              style={{ color: 'var(--color-primary)' }}
            >
              PocketBooster
            </h1>
            <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
              {isConnected ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
              <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
            </div>
          </div>

          {/* Ultra-Compact Quick Stats */}
          <div className="header-stats">
            <div className="stat-item">
              <div className="stat-label">Balance</div>
              <div className="stat-value">${balance.toFixed(2)}</div>
            </div>
            <div className="stat-item">
              <div className="stat-label">P/L</div>
              <div className={`stat-value ${profit >= 0 ? 'positive' : 'negative'}`}>
                {profit >= 0 ? '+' : ''}${profit.toFixed(2)}
              </div>
            </div>
            <div className="stat-item">
              <div className="stat-label">Win Rate</div>
              <div className="stat-value">{winRate.toFixed(1)}%</div>
            </div>
            <div className="stat-item">
              <div className="stat-label">Trades</div>
              <div className="stat-value">{totalTrades}</div>
            </div>
            <div className="stat-item">
              <div className="stat-label">Active</div>
              <div className="stat-value" style={{ color: 'var(--color-info)' }}>
                {activeTrades}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Ultra-Compact Navigation Tabs */}
      <nav className="nav-tabs">
        <div className="flex gap-[var(--spacing-2xl)]">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              >
                <Icon className="w-3 h-3" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>
      </nav>

      {/* Ultra-Compact Main Content */}
      <main className="flex-1 overflow-hidden" style={{ background: 'var(--color-bg-primary)' }}>
        {activeTab === 'trading' && (
          <div className="h-full flex">
            <div
              className="w-[280px] flex-shrink-0"
              style={{ borderRight: '1px solid var(--color-border-primary)' }}
            >
              <TradingControls onDataUpdate={loadDashboardData} />
            </div>
            <div
              className="flex-1"
              style={{ borderRight: '1px solid var(--color-border-primary)' }}
            >
              <AssetSelector />
            </div>
            <div className="w-[300px] flex-shrink-0">
              <SignalDisplay />
            </div>
          </div>
        )}

        {activeTab === 'performance' && (
          <div
            className="h-full flex items-center justify-center"
            style={{ background: 'var(--color-bg-primary)' }}
          >
            <div className="text-center" style={{ color: 'var(--color-text-secondary)' }}>
              <BarChart3 className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-xl">Performance Panel</p>
              <p className="text-sm mt-2">Temporarily disabled - use Analytics tab instead</p>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && <AnalyticsDashboard />}

        {activeTab === 'logs' && <LogViewer />}

        {activeTab === 'config' && <ConfigPanel />}
      </main>
    </div>
  )
}

export default TradingDashboard
