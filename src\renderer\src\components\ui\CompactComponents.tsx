import React from 'react'
import { LucideIcon } from 'lucide-react'
import { cva, type VariantProps } from 'class-variance-authority'
import { clsx } from 'clsx'

// Compact Button Variants
const buttonVariants = cva(
  'inline-flex items-center justify-center gap-1 font-medium transition-all duration-150 ease-out cursor-pointer disabled:cursor-not-allowed disabled:opacity-50 border',
  {
    variants: {
      variant: {
        primary: 'bg-[var(--color-primary)] text-white border-[var(--color-primary-border)] hover:bg-[var(--color-primary-hover)] hover:-translate-y-px',
        danger: 'bg-[var(--color-danger)] text-white border-[var(--color-danger-border)] hover:bg-[var(--color-danger-hover)] hover:-translate-y-px',
        secondary: 'bg-[var(--color-bg-elevated)] text-[var(--color-text-primary)] border-[var(--color-border-primary)] hover:bg-[var(--color-bg-tertiary)] hover:border-[var(--color-border-muted)] hover:-translate-y-px',
        ghost: 'bg-transparent text-[var(--color-text-secondary)] border-transparent hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-text-primary)]'
      },
      size: {
        xs: 'h-[24px] px-[6px] text-[10px] rounded-[2px]',
        sm: 'h-[var(--button-height)] px-[var(--spacing-lg)] text-[var(--text-sm)] rounded-[var(--radius-sm)]',
        md: 'h-[var(--button-lg-height)] px-[var(--spacing-xl)] text-[var(--text-sm)] rounded-[var(--radius-sm)]',
        lg: 'h-[36px] px-[16px] text-[var(--text-base)] rounded-[var(--radius-sm)]'
      }
    },
    defaultVariants: {
      variant: 'primary',
      size: 'sm'
    }
  }
)

interface CompactButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  icon?: LucideIcon
  loading?: boolean
}

export const CompactButton = React.forwardRef<HTMLButtonElement, CompactButtonProps>(
  ({ className, variant, size, icon: Icon, loading, children, ...props }, ref) => {
    return (
      <button
        className={clsx(buttonVariants({ variant, size }), className)}
        ref={ref}
        disabled={loading || props.disabled}
        {...props}
      >
        {loading ? (
          <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin" />
        ) : Icon ? (
          <Icon className="w-3 h-3" />
        ) : null}
        {children}
      </button>
    )
  }
)

// Compact Input Component
interface CompactInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
}

export const CompactInput = React.forwardRef<HTMLInputElement, CompactInputProps>(
  ({ className, label, error, ...props }, ref) => {
    return (
      <div className="flex flex-col gap-[var(--spacing-xs)]">
        {label && (
          <label className="text-[var(--text-xs)] font-semibold text-[var(--color-text-secondary)] uppercase tracking-wide leading-none">
            {label}
          </label>
        )}
        <input
          className={clsx(
            'bg-[var(--color-bg-secondary)] border border-[var(--color-border-primary)] text-[var(--color-text-primary)] rounded-[var(--radius-sm)] px-[var(--spacing-md)] py-[var(--spacing-sm)] text-[var(--text-sm)] leading-none transition-all duration-150 ease-out min-h-[var(--input-height)] w-full',
            'focus:outline-none focus:border-[var(--color-border-focus)] focus:shadow-[0_0_0_1px_var(--color-primary-light)] focus:bg-[var(--color-bg-tertiary)]',
            'hover:border-[var(--color-border-muted)]',
            'placeholder:text-[var(--color-text-muted)] placeholder:text-[var(--text-xs)]',
            error && 'border-[var(--color-danger)] focus:border-[var(--color-danger)]',
            className
          )}
          ref={ref}
          {...props}
        />
        {error && (
          <span className="text-[var(--text-xs)] text-[var(--color-danger)] leading-none">
            {error}
          </span>
        )}
      </div>
    )
  }
)

// Compact Select Component
interface CompactSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string
  error?: string
  options: { value: string; label: string }[]
}

export const CompactSelect = React.forwardRef<HTMLSelectElement, CompactSelectProps>(
  ({ className, label, error, options, ...props }, ref) => {
    return (
      <div className="flex flex-col gap-[var(--spacing-xs)]">
        {label && (
          <label className="text-[var(--text-xs)] font-semibold text-[var(--color-text-secondary)] uppercase tracking-wide leading-none">
            {label}
          </label>
        )}
        <select
          className={clsx(
            'bg-[var(--color-bg-secondary)] border border-[var(--color-border-primary)] text-[var(--color-text-primary)] rounded-[var(--radius-sm)] px-[var(--spacing-md)] py-[var(--spacing-sm)] text-[var(--text-sm)] leading-none transition-all duration-150 ease-out min-h-[var(--input-height)] w-full cursor-pointer',
            'focus:outline-none focus:border-[var(--color-border-focus)] focus:shadow-[0_0_0_1px_var(--color-primary-light)] focus:bg-[var(--color-bg-tertiary)]',
            'hover:border-[var(--color-border-muted)]',
            error && 'border-[var(--color-danger)] focus:border-[var(--color-danger)]',
            className
          )}
          ref={ref}
          {...props}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {error && (
          <span className="text-[var(--text-xs)] text-[var(--color-danger)] leading-none">
            {error}
          </span>
        )}
      </div>
    )
  }
)

// Compact Card Component
interface CompactCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  icon?: LucideIcon
  headerAction?: React.ReactNode
}

export const CompactCard = React.forwardRef<HTMLDivElement, CompactCardProps>(
  ({ className, title, icon: Icon, headerAction, children, ...props }, ref) => {
    return (
      <div
        className={clsx(
          'bg-[var(--color-bg-secondary)] border border-[var(--color-border-primary)] rounded-[var(--radius-sm)] p-[var(--card-padding)] transition-all duration-150 ease-out hover:border-[var(--color-border-muted)]',
          className
        )}
        ref={ref}
        {...props}
      >
        {title && (
          <div className="border-b border-[var(--color-border-primary)] pb-[var(--spacing-sm)] mb-[var(--spacing-md)] flex items-center justify-between">
            <h3 className="text-[var(--text-lg)] font-semibold text-[var(--color-text-primary)] flex items-center gap-[var(--spacing-xs)] leading-tight">
              {Icon && <Icon className="w-4 h-4" />}
              {title}
            </h3>
            {headerAction}
          </div>
        )}
        {children}
      </div>
    )
  }
)

// Compact Status Indicator
interface CompactStatusProps {
  status: 'connected' | 'disconnected' | 'running' | 'stopped' | 'warning'
  label: string
  icon?: LucideIcon
}

export const CompactStatus: React.FC<CompactStatusProps> = ({ status, label, icon: Icon }) => {
  const statusStyles = {
    connected: 'bg-[var(--color-primary-light)] text-[var(--color-primary)] border-[var(--color-primary-border)]',
    running: 'bg-[var(--color-primary-light)] text-[var(--color-primary)] border-[var(--color-primary-border)]',
    disconnected: 'bg-[var(--color-danger-light)] text-[var(--color-danger)] border-[var(--color-danger-border)]',
    stopped: 'bg-[var(--color-bg-elevated)] text-[var(--color-text-muted)] border-[var(--color-border-primary)]',
    warning: 'bg-[var(--color-warning-light)] text-[var(--color-warning)] border-[var(--color-warning)]'
  }

  return (
    <div
      className={clsx(
        'inline-flex items-center gap-[var(--spacing-sm)] px-[var(--spacing-md)] py-[var(--spacing-sm)] rounded-[var(--radius-md)] text-[var(--text-xs)] font-semibold uppercase tracking-wide border',
        statusStyles[status]
      )}
    >
      {Icon && <Icon className="w-3 h-3" />}
      <span>{label}</span>
    </div>
  )
}

// Compact Form Row
interface CompactFormRowProps extends React.HTMLAttributes<HTMLDivElement> {
  columns?: number
}

export const CompactFormRow: React.FC<CompactFormRowProps> = ({ 
  className, 
  children, 
  columns = 2,
  ...props 
}) => {
  return (
    <div
      className={clsx(
        'grid gap-[var(--spacing-sm)] mb-[var(--spacing-sm)]',
        columns === 1 && 'grid-cols-1',
        columns === 2 && 'grid-cols-1 sm:grid-cols-2',
        columns === 3 && 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
        columns === 4 && 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Compact Trading Buttons
interface CompactTradingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  action: 'buy' | 'sell'
  icon?: LucideIcon
}

export const CompactTradingButton = React.forwardRef<HTMLButtonElement, CompactTradingButtonProps>(
  ({ className, action, icon: Icon, children, ...props }, ref) => {
    const baseClasses = 'flex items-center justify-center gap-[var(--spacing-xs)] font-bold text-[var(--text-sm)] uppercase tracking-wide leading-none transition-all duration-150 ease-out cursor-pointer min-h-[var(--button-lg-height)] px-[var(--spacing-lg)] rounded-[var(--radius-sm)] border'
    
    const actionClasses = action === 'buy' 
      ? 'bg-[var(--color-primary)] text-white border-[var(--color-primary)] hover:bg-[var(--color-primary-hover)] hover:border-[var(--color-primary-hover)] hover:-translate-y-px'
      : 'bg-[var(--color-danger)] text-white border-[var(--color-danger)] hover:bg-[var(--color-danger-hover)] hover:border-[var(--color-danger-hover)] hover:-translate-y-px'

    return (
      <button
        className={clsx(baseClasses, actionClasses, className)}
        ref={ref}
        {...props}
      >
        {Icon && <Icon className="w-3 h-3" />}
        {children}
      </button>
    )
  }
)
